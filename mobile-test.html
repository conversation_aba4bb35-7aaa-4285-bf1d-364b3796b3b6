<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端测试 - TikTok Clone</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            background: #ff3040;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f8f8;
            border-radius: 5px;
        }
        .orientation-info {
            font-weight: bold;
            color: #ff3040;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h2>📱 移动端测试页面</h2>
        <p>这个页面帮助你测试TikTok Clone在不同移动设备上的表现</p>
        
        <div class="info-item">
            <strong>屏幕尺寸:</strong> <span id="screenSize"></span>
        </div>
        
        <div class="info-item">
            <strong>设备方向:</strong> <span id="orientation" class="orientation-info"></span>
        </div>
        
        <div class="info-item">
            <strong>用户代理:</strong> <span id="userAgent"></span>
        </div>
        
        <div class="info-item">
            <strong>触摸支持:</strong> <span id="touchSupport"></span>
        </div>
    </div>

    <a href="http://localhost:3000" class="test-button">
        🚀 打开 TikTok Clone
    </a>

    <div class="test-info">
        <h3>📋 测试清单</h3>
        <ul>
            <li>✅ 竖屏模式下的滑动切换</li>
            <li>✅ 横屏模式下的滑动切换</li>
            <li>✅ 视频自动播放和暂停</li>
            <li>✅ 图片集左右滑动切换</li>
            <li>✅ 用户信息显示</li>
            <li>✅ 进度指示器</li>
            <li>✅ 屏幕方向切换适配</li>
        </ul>
    </div>

    <div class="test-info">
        <h3>🎮 操作说明</h3>
        <ul>
            <li><strong>上下滑动:</strong> 切换视频/图片集</li>
            <li><strong>左右滑动:</strong> 在图片集中切换图片</li>
            <li><strong>点击视频:</strong> 播放/暂停</li>
            <li><strong>旋转屏幕:</strong> 测试横竖屏适配</li>
        </ul>
    </div>

    <script>
        function updateDeviceInfo() {
            document.getElementById('screenSize').textContent = 
                `${window.innerWidth} x ${window.innerHeight}`;
            
            const orientation = window.innerHeight > window.innerWidth ? '竖屏' : '横屏';
            document.getElementById('orientation').textContent = orientation;
            
            document.getElementById('userAgent').textContent = 
                navigator.userAgent.substring(0, 50) + '...';
            
            document.getElementById('touchSupport').textContent = 
                'ontouchstart' in window ? '支持' : '不支持';
        }

        // 初始化设备信息
        updateDeviceInfo();

        // 监听屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });

        window.addEventListener('resize', updateDeviceInfo);

        // 添加一些调试信息
        console.log('移动端测试页面已加载');
        console.log('屏幕尺寸:', window.innerWidth, 'x', window.innerHeight);
        console.log('设备像素比:', window.devicePixelRatio);
    </script>
</body>
</html>
