<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>短视频服务 API 接口测试</h1>
    
    <div class="test-section">
        <h3>1. 内容列表接口测试</h3>
        <button class="test-button" onclick="testContentsList()">测试内容列表</button>
        <div id="contents-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 媒体文件接口测试</h3>
        <input type="text" id="content-id" placeholder="输入内容ID" style="padding: 8px; margin: 5px;">
        <button class="test-button" onclick="testContentMedia()">测试媒体文件</button>
        <div id="media-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 评论接口测试</h3>
        <input type="text" id="comment-content-id" placeholder="输入内容ID" style="padding: 8px; margin: 5px;">
        <button class="test-button" onclick="testComments()">测试评论列表</button>
        <button class="test-button" onclick="testCreateComment()">测试创建评论</button>
        <div id="comment-result" class="result"></div>
    </div>

    <script>
        const API_CONFIG = {
            BASE_URL: 'http://localhost:8080',
            API_PATH: '/api',
            HEADERS: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testContentsList() {
            try {
                const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/contents/list?current=1&size=5`;
                const response = await fetch(url, {
                    headers: API_CONFIG.HEADERS
                });

                const data = await response.json();
                showResult('contents-result', {
                    status: response.status,
                    data: data
                });
            } catch (error) {
                showResult('contents-result', {
                    error: error.message
                }, true);
            }
        }

        async function testContentMedia() {
            const contentId = document.getElementById('content-id').value;
            if (!contentId) {
                showResult('media-result', { error: '请输入内容ID' }, true);
                return;
            }

            try {
                const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/content-media/content/${contentId}`;
                const response = await fetch(url, {
                    headers: API_CONFIG.HEADERS
                });

                const data = await response.json();
                showResult('media-result', {
                    status: response.status,
                    data: data
                });
            } catch (error) {
                showResult('media-result', {
                    error: error.message
                }, true);
            }
        }

        async function testComments() {
            const contentId = document.getElementById('comment-content-id').value;
            if (!contentId) {
                showResult('comment-result', { error: '请输入内容ID' }, true);
                return;
            }

            try {
                const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/comments?contentId=${contentId}&current=1&size=10`;
                const response = await fetch(url, {
                    headers: API_CONFIG.HEADERS
                });

                const data = await response.json();
                showResult('comment-result', {
                    status: response.status,
                    data: data
                });
            } catch (error) {
                showResult('comment-result', {
                    error: error.message
                }, true);
            }
        }

        async function testCreateComment() {
            const contentId = document.getElementById('comment-content-id').value;
            if (!contentId) {
                showResult('comment-result', { error: '请输入内容ID' }, true);
                return;
            }

            try {
                const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/comments`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: API_CONFIG.HEADERS,
                    body: JSON.stringify({
                        contentId: parseInt(contentId),
                        userId: 1001,
                        commentText: '这是一个测试评论'
                    })
                });

                const data = await response.json();
                showResult('comment-result', {
                    status: response.status,
                    data: data
                });
            } catch (error) {
                showResult('comment-result', {
                    error: error.message
                }, true);
            }
        }
    </script>
</body>
</html>
