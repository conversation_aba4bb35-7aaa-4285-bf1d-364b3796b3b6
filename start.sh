#!/bin/bash

echo "🚀 启动 TikTok Clone 应用..."
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js"
    echo "请先安装 Node.js 20.x 或更高版本"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "⚠️  警告: 检测到 Node.js 版本较低 ($(node -v))"
    echo "建议使用 Node.js 20.x 或更高版本"
fi

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

echo "✅ 环境检查完成"
echo ""

# 启动服务器
echo "🌟 启动服务器..."
echo "📱 应用将在 http://localhost:3000 运行"
echo "🔄 按 Ctrl+C 停止服务器"
echo ""

npm start
