const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// API路由
app.get('/api/media', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        res.json(mediaData);
    } catch (error) {
        console.error('Error loading media data:', error);
        res.status(500).json({ error: 'Failed to load media data' });
    }
});

// 获取单个媒体项
app.get('/api/media/:id', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        const mediaItem = mediaData[parseInt(req.params.id)];
        
        if (mediaItem) {
            res.json(mediaItem);
        } else {
            res.status(404).json({ error: 'Media not found' });
        }
    } catch (error) {
        console.error('Error loading media item:', error);
        res.status(500).json({ error: 'Failed to load media item' });
    }
});

// 点赞API
app.post('/api/media/:id/like', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        const mediaIndex = parseInt(req.params.id);
        
        if (mediaData[mediaIndex]) {
            mediaData[mediaIndex].stats.likes += 1;
            fs.writeFileSync(dataPath, JSON.stringify(mediaData, null, 2));
            res.json({ 
                success: true, 
                likes: mediaData[mediaIndex].stats.likes 
            });
        } else {
            res.status(404).json({ error: 'Media not found' });
        }
    } catch (error) {
        console.error('Error updating likes:', error);
        res.status(500).json({ error: 'Failed to update likes' });
    }
});

// 评论API
app.get('/api/media/:id/comments', (req, res) => {
    // 模拟评论数据
    const comments = [
        {
            id: 1,
            user: { name: 'user1', avatar: 'https://picsum.photos/50/50?random=101' },
            text: '太棒了！👍',
            timestamp: new Date().toISOString()
        },
        {
            id: 2,
            user: { name: 'user2', avatar: 'https://picsum.photos/50/50?random=102' },
            text: '很有创意的内容',
            timestamp: new Date().toISOString()
        },
        {
            id: 3,
            user: { name: 'user3', avatar: 'https://picsum.photos/50/50?random=103' },
            text: '学到了很多！',
            timestamp: new Date().toISOString()
        }
    ];
    
    res.json(comments);
});

// 添加评论API
app.post('/api/media/:id/comments', (req, res) => {
    const { text, user } = req.body;
    
    if (!text || !user) {
        return res.status(400).json({ error: 'Missing required fields' });
    }
    
    const newComment = {
        id: Date.now(),
        user: user,
        text: text,
        timestamp: new Date().toISOString()
    };
    
    // 这里应该保存到数据库，现在只是返回新评论
    res.json(newComment);
});

// 分享统计API
app.post('/api/media/:id/share', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        const mediaIndex = parseInt(req.params.id);
        
        if (mediaData[mediaIndex]) {
            mediaData[mediaIndex].stats.shares += 1;
            fs.writeFileSync(dataPath, JSON.stringify(mediaData, null, 2));
            res.json({ 
                success: true, 
                shares: mediaData[mediaIndex].stats.shares 
            });
        } else {
            res.status(404).json({ error: 'Media not found' });
        }
    } catch (error) {
        console.error('Error updating shares:', error);
        res.status(500).json({ error: 'Failed to update shares' });
    }
});

// 搜索API
app.get('/api/search', (req, res) => {
    const { q } = req.query;
    
    if (!q) {
        return res.status(400).json({ error: 'Search query is required' });
    }
    
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        
        const results = mediaData.filter(item => 
            item.description.toLowerCase().includes(q.toLowerCase()) ||
            item.user.name.toLowerCase().includes(q.toLowerCase()) ||
            item.music.toLowerCase().includes(q.toLowerCase())
        );
        
        res.json(results);
    } catch (error) {
        console.error('Error searching media:', error);
        res.status(500).json({ error: 'Search failed' });
    }
});

// 用户统计API
app.get('/api/stats', (req, res) => {
    try {
        const dataPath = path.join(__dirname, 'public', 'media', 'sample-data.json');
        const mediaData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        
        const totalLikes = mediaData.reduce((sum, item) => sum + item.stats.likes, 0);
        const totalComments = mediaData.reduce((sum, item) => sum + item.stats.comments, 0);
        const totalShares = mediaData.reduce((sum, item) => sum + item.stats.shares, 0);
        const totalVideos = mediaData.filter(item => item.type === 'video').length;
        const totalImageSets = mediaData.filter(item => item.type === 'images').length;
        
        res.json({
            totalContent: mediaData.length,
            totalVideos,
            totalImageSets,
            totalLikes,
            totalComments,
            totalShares
        });
    } catch (error) {
        console.error('Error getting stats:', error);
        res.status(500).json({ error: 'Failed to get stats' });
    }
});

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 404处理
app.use((req, res) => {
    res.status(404).json({ error: 'Not found' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 TikTok Clone server is running on http://localhost:${PORT}`);
    console.log(`📱 Open your browser and visit the URL above to see the app`);
});

module.exports = app;
