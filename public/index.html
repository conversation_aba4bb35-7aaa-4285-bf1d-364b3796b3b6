<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Clone</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 主内容区域 -->
        <div class="content-container" id="contentContainer">
            <!-- 内容项将通过JavaScript动态生成 -->
        </div>



        <!-- 右侧操作栏 -->
        <div class="right-sidebar">
            <div class="avatar-container">
                <div class="avatar">
                    <img src="" alt="User Avatar" id="userAvatar">
                </div>
            </div>
            <button class="comment-btn" id="commentBtn">
                <i class="fas fa-comment"></i>
                <span class="comment-count" id="commentCount">0</span>
            </button>
        </div>

        <!-- 底部用户信息 -->
        <div class="bottom-user-info">
            <div class="username" id="username">@username</div>
            <div class="description" id="description">
                <span class="description-text">内容描述...</span>
                <button class="expand-btn" id="expandBtn" style="display: none;">展开</button>
            </div>
        </div>



        <!-- 加载指示器 -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="spinner"></div>
        </div>

        <!-- 内容结束提示 -->
        <div class="end-message" id="endMessage" style="display: none;">
            <div class="end-content">
                <div class="end-icon">📱</div>
                <div class="end-title">暂时没有更多内容了</div>
                <div class="end-subtitle">已经刷到底啦～</div>
                <button class="restart-btn" id="restartBtn">重新开始</button>
            </div>
        </div>

        <!-- 评论面板 -->
        <div class="comment-panel" id="commentPanel" style="display: none;">
            <div class="comment-header">
                <div class="comment-title">评论</div>
                <button class="comment-close" id="commentClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="comment-list" id="commentList">
                <!-- 评论列表将通过JavaScript动态生成 -->
            </div>

            <div class="comment-input-area">
                <div class="comment-input-container">
                    <input type="text" class="comment-input" id="commentInput" placeholder="说点什么...">
                    <button class="comment-send" id="commentSend">发送</button>
                </div>
                <div class="reply-info" id="replyInfo" style="display: none;">
                    <span class="reply-text">回复 <span id="replyTarget">@用户</span></span>
                    <button class="reply-cancel" id="replyCancel">取消</button>
                </div>
            </div>
        </div>

        <!-- 图片集指示器 -->
        <div class="image-indicators" id="imageIndicators" style="display: none;">
            <!-- 图片指示器将通过JavaScript动态生成 -->
        </div>

        <!-- 错误提示 -->
        <div class="error-toast" id="errorToast" style="display: none;">
            <div class="error-content">
                <i class="fas fa-exclamation-circle"></i>
                <span class="error-message" id="errorMessage">出现错误</span>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
