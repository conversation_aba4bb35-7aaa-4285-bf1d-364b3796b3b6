// API配置
const API_CONFIG = {
    BASE_URL: 'http://localhost:8080',
    API_PATH: '/api',
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    RETRY_TIMES: 3,
    RETRY_DELAY: 1000
};

class TikTokClone {
    constructor() {
        this.currentIndex = 0;
        this.currentImageIndex = 0;
        this.mediaData = [];
        this.isTransitioning = false;
        this.touchStartY = 0;
        this.touchEndY = 0;
        this.touchStartX = 0;
        this.touchEndX = 0;
        this.isVerticalSwipe = false;
        this.comments = {}; // 存储每个媒体的评论数据
        this.replyingTo = null; // 当前回复的评论
        this.isFirstLoad = true; // 标记是否首次加载
        this.currentPage = 1; // 当前页码
        this.pageSize = 10; // 每页数量
        this.hasMore = true; // 是否还有更多内容
        this.isLoading = false; // 是否正在加载
        this.currentUserId = 1001; // 当前用户ID（实际应用中应该从登录状态获取）

        this.init();
    }

    async init() {
        await this.loadMediaData();
        this.setupEventListeners();
        this.renderContent();
        this.handleOrientationChange();
    }

    async loadMediaData(loadMore = false) {
        if (this.isLoading || (!loadMore && !this.hasMore && this.mediaData.length > 0)) {
            return;
        }

        this.isLoading = true;
        if (!loadMore) {
            this.showLoading();
        }

        try {
            const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/contents/list?current=${this.currentPage}&size=${this.pageSize}`;
            const response = await this.fetchWithRetry(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            // 检查API响应格式
            if (result.code !== 200) {
                throw new Error(`API error: ${result.message}`);
            }

            const data = result.data;

            // 转换API数据格式为应用内部格式
            const newContents = await this.transformApiData(data.records);

            if (loadMore) {
                this.mediaData = [...this.mediaData, ...newContents];
            } else {
                this.mediaData = newContents;
            }

            // 根据分页信息判断是否还有更多内容
            this.hasMore = data.current < data.pages;
            this.currentPage++;

            console.log(`✅ 成功加载 ${newContents.length} 个媒体项，总计 ${this.mediaData.length} 个`);

        } catch (error) {
            console.error('Failed to load media data:', error);

            // 如果是首次加载失败，使用示例数据作为后备
            if (!loadMore && this.mediaData.length === 0) {
                this.mediaData = this.getExampleData();
                console.log('🔄 使用示例数据作为后备');
            } else {
                this.showError('加载内容失败，请检查网络连接');
            }
        } finally {
            this.isLoading = false;
            if (!loadMore) {
                this.hideLoading();
            }
        }
    }

    getExampleData() {
        return [
            {
                type: 'video',
                src: 'media/videos/sample1.mp4',
                poster: 'media/images/poster1.jpg',
                user: { name: 'user1', avatar: 'media/images/avatar1.jpg' },
                description: '这是一个示例视频 #TikTok #短视频',
                music: '原声 - user1',
                stats: { likes: 1234, comments: 56, shares: 78 }
            },
            {
                type: 'images',
                images: [
                    'media/images/image1.jpg',
                    'media/images/image2.jpg',
                    'media/images/image3.jpg'
                ],
                user: { name: 'user2', avatar: 'media/images/avatar2.jpg' },
                description: '美丽的风景照片集 #摄影 #风景',
                music: '原声 - user2',
                stats: { likes: 2345, comments: 89, shares: 123 }
            },
            {
                type: 'video',
                src: 'media/videos/sample2.mp4',
                poster: 'media/images/poster2.jpg',
                user: { name: 'user3', avatar: 'media/images/avatar3.jpg' },
                description: '有趣的短视频内容 #搞笑 #娱乐',
                music: '热门音乐 - Artist',
                stats: { likes: 3456, comments: 234, shares: 567 }
            }
        ];
    }

    // 转换API数据格式为应用内部格式
    async transformApiData(apiContents) {
        const transformedContents = [];

        for (const content of apiContents) {
            // 获取媒体文件
            const mediaFiles = await this.getContentMedia(content.id);
            const firstMedia = mediaFiles[0];

            if (content.contentType === 'video') {
                transformedContents.push({
                    id: content.id,
                    type: 'video',
                    src: firstMedia?.mediaUrl || '',
                    poster: this.generatePosterUrl(firstMedia?.mediaUrl),
                    user: {
                        name: content.username,
                        avatar: content.avatarUrl || 'https://picsum.photos/100/100?random=' + content.userId
                    },
                    description: content.description || '',
                    music: '原声 - ' + content.username,
                    stats: {
                        likes: 0, // API中没有提供，使用默认值
                        comments: 0,
                        shares: 0
                    }
                });
            } else {
                // 处理图片集
                transformedContents.push({
                    id: content.id,
                    type: 'images',
                    images: mediaFiles.map(file => file.mediaUrl),
                    user: {
                        name: content.username,
                        avatar: content.avatarUrl || 'https://picsum.photos/100/100?random=' + content.userId
                    },
                    description: content.description || '',
                    music: '原声 - ' + content.username,
                    stats: {
                        likes: 0,
                        comments: 0,
                        shares: 0
                    }
                });
            }
        }

        return transformedContents;
    }

    // 根据内容ID获取媒体文件
    async getContentMedia(contentId) {
        try {
            const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/content-media/content/${contentId}`;
            const response = await this.fetchWithRetry(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.code !== 200) {
                throw new Error(`API error: ${result.message}`);
            }

            return result.data || [];
        } catch (error) {
            console.error('Failed to load media files:', error);
            return [];
        }
    }

    // 生成视频封面图URL（如果API没有提供）
    generatePosterUrl(videoUrl) {
        if (!videoUrl) {
            // 生成随机封面图
            const randomId = Math.floor(Math.random() * 1000);
            return `https://picsum.photos/400/600?random=${randomId}`;
        }

        // 尝试从视频URL生成封面图URL
        if (videoUrl.includes('.mp4')) {
            return videoUrl.replace('.mp4', '_poster.jpg');
        }

        // 如果无法生成，使用默认封面
        const videoId = videoUrl.split('/').pop().split('.')[0];
        return `https://picsum.photos/400/600?random=${videoId}`;
    }

    // 带重试的fetch请求
    async fetchWithRetry(url, options = {}, retryCount = 0) {
        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    ...API_CONFIG.HEADERS,
                    ...options.headers
                }
            });
            return response;
        } catch (error) {
            if (retryCount < API_CONFIG.RETRY_TIMES) {
                console.log(`请求失败，${API_CONFIG.RETRY_DELAY}ms后重试 (${retryCount + 1}/${API_CONFIG.RETRY_TIMES})`);
                await this.delay(API_CONFIG.RETRY_DELAY);
                return this.fetchWithRetry(url, options, retryCount + 1);
            }
            throw error;
        }
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    setupEventListeners() {
        const container = document.getElementById('contentContainer');
        
        // 触摸事件
        container.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        container.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 鼠标事件（用于桌面测试）
        container.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        container.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        


        // 屏幕方向变化事件
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleOrientationChange(), 100);
        });

        // 窗口大小变化事件
        window.addEventListener('resize', () => this.handleOrientationChange());

        // 页面可见性变化事件 - 当页面失去焦点时暂停所有媒体
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAllMedia();
                console.log('页面失去焦点，暂停所有媒体');
            }
        });

        // 窗口失去焦点时暂停所有媒体
        window.addEventListener('blur', () => {
            this.pauseAllMedia();
            console.log('窗口失去焦点，暂停所有媒体');
        });

        // 重新开始按钮事件
        document.getElementById('restartBtn').addEventListener('click', () => this.restartContent());

        // 评论相关事件
        document.getElementById('commentBtn').addEventListener('click', () => this.openCommentPanel());
        document.getElementById('commentClose').addEventListener('click', () => this.closeCommentPanel());
        document.getElementById('commentSend').addEventListener('click', () => this.sendComment());
        document.getElementById('commentInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendComment();
        });
        document.getElementById('replyCancel').addEventListener('click', () => this.cancelReply());

        // 描述展开功能
        document.getElementById('expandBtn').addEventListener('click', () => this.toggleDescription());
        

    }



    handleTouchStart(e) {
        this.touchStartY = e.touches[0].clientY;
        this.touchStartX = e.touches[0].clientX;
        this.isVerticalSwipe = false;
    }

    handleTouchEnd(e) {
        this.touchEndY = e.changedTouches[0].clientY;
        this.touchEndX = e.changedTouches[0].clientX;
        this.handleSwipe();
    }

    handleMouseDown(e) {
        this.touchStartY = e.clientY;
        this.touchStartX = e.clientX;
    }

    handleMouseUp(e) {
        this.touchEndY = e.clientY;
        this.touchEndX = e.clientX;
        this.handleSwipe();
    }

    handleSwipe() {
        const swipeThreshold = 30;
        const verticalDiff = this.touchStartY - this.touchEndY;
        const horizontalDiff = this.touchStartX - this.touchEndX;

        // 判断是垂直滑动还是水平滑动
        if (Math.abs(verticalDiff) > Math.abs(horizontalDiff)) {
            // 垂直滑动 - 切换内容
            if (Math.abs(verticalDiff) > swipeThreshold) {
                if (verticalDiff > 0) {
                    this.nextContent();
                } else {
                    this.previousContent();
                }
            }
        } else {
            // 水平滑动 - 切换图片（仅在图片集时）
            const currentMedia = this.getCurrentMedia();
            if (currentMedia.type === 'images' && Math.abs(horizontalDiff) > swipeThreshold) {
                if (horizontalDiff > 0) {
                    this.nextImage();
                } else {
                    this.previousImage();
                }
            }
        }
    }

    handleKeyDown(e) {
        switch(e.key) {
            case 'ArrowUp':
                e.preventDefault();
                this.previousContent();
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.nextContent();
                break;
            case 'ArrowLeft':
                if (this.getCurrentMedia().type === 'images') {
                    e.preventDefault();
                    this.previousImage();
                }
                break;
            case 'ArrowRight':
                if (this.getCurrentMedia().type === 'images') {
                    e.preventDefault();
                    this.nextImage();
                }
                break;
            case ' ':
                e.preventDefault();
                this.togglePlayPause();
                break;
        }
    }

    renderContent() {
        const container = document.getElementById('contentContainer');
        container.innerHTML = '';

        this.mediaData.forEach((media, index) => {
            const contentItem = this.createContentItem(media, index);
            container.appendChild(contentItem);

            // 为视频添加点击事件和加载事件
            const video = contentItem.querySelector('video');
            if (video) {
                video.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggleVideoPlayPause(video);
                });

                // 添加视频加载事件监听
                video.addEventListener('loadstart', () => {
                    console.log('视频开始加载');
                });

                video.addEventListener('loadedmetadata', () => {
                    console.log('视频元数据加载完成');
                });

                video.addEventListener('canplay', () => {
                    console.log('视频可以播放');
                });

                video.addEventListener('error', (e) => {
                    console.error('视频加载错误:', e);
                    this.showError('视频加载失败');
                });
            }
        });

        this.updateActiveContent();
        this.updateBottomInfo();
    }

    createContentItem(media, index) {
        const item = document.createElement('div');
        item.className = 'content-item';
        item.dataset.index = index;
        
        if (media.type === 'video') {
            item.innerHTML = this.createVideoContent(media);
        } else if (media.type === 'images') {
            item.innerHTML = this.createImageContent(media);
        }
        
        return item;
    }

    createVideoContent(media) {
        return `
            <div class="video-container">
                <video
                    src="${media.src}"
                    poster="${media.poster}"
                    loop
                    playsinline
                    preload="metadata"
                    data-media-id="${media.id || 'unknown'}"
                >
                    您的浏览器不支持视频播放。
                </video>
            </div>
        `;
    }

    createImageContent(media) {
        const imagesHtml = media.images.map(img => 
            `<div class="image-slide"><img src="${img}" alt="Image"></div>`
        ).join('');
        
        return `
            <div class="image-container">
                <div class="image-slider" style="transform: translateX(0%)">
                    ${imagesHtml}
                </div>
            </div>
        `;
    }

    updateActiveContent() {
        const items = document.querySelectorAll('.content-item');
        items.forEach((item, index) => {
            item.classList.remove('active', 'prev', 'next');
            
            if (index === this.currentIndex) {
                item.classList.add('active');
                this.playCurrentMedia();
            } else if (index < this.currentIndex) {
                item.classList.add('prev');
            } else {
                item.classList.add('next');
            }
        });
        
        this.updateImageNavigation();
    }

    // 暂停所有媒体内容
    pauseAllMedia() {
        // 暂停所有视频
        document.querySelectorAll('video').forEach(video => {
            if (!video.paused) {
                video.pause();
                console.log('暂停视频:', video.src);
            }
            // 重置视频到开始位置
            video.currentTime = 0;

            // 显示播放按钮
            const videoContainer = video.parentElement;
            if (videoContainer && !videoContainer.querySelector('.play-overlay')) {
                this.showPlayButton(video);
            }
        });

        // 清除所有自动播放的定时器（如果有的话）
        if (this.autoPlayTimer) {
            clearTimeout(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }

        console.log('已暂停所有媒体内容');
    }

    playCurrentMedia() {
        // 首先暂停所有其他媒体
        this.pauseAllMedia();

        const currentItem = document.querySelector('.content-item.active');
        if (!currentItem) return;

        const video = currentItem.querySelector('video');

        // 播放当前视频
        if (video) {
            video.currentTime = 0;
            video.muted = false; // 不静音，保持原声

            // 移除当前视频的播放按钮
            const existingPlayButton = currentItem.querySelector('.play-overlay');
            if (existingPlayButton) {
                existingPlayButton.remove();
            }

            // 首次加载不自动播放，显示播放按钮和封面
            if (this.isFirstLoad) {
                this.showPlayButton(video);
                this.isFirstLoad = false;
                return;
            }

            // 切换时自动播放
            const playPromise = video.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('视频自动播放成功');
                }).catch(e => {
                    console.log('自动播放失败，显示播放按钮:', e);
                    this.showPlayButton(video);
                });
            }
        }
    }

    showPlayButton(video) {
        // 移除已存在的播放按钮
        const existingOverlay = video.parentElement.querySelector('.play-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // 创建播放按钮覆盖层
        const playOverlay = document.createElement('div');
        playOverlay.className = 'play-overlay';
        playOverlay.innerHTML = '<i class="fas fa-play"></i>';
        playOverlay.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            cursor: pointer;
            z-index: 10;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        `;

        playOverlay.addEventListener('click', () => {
            this.toggleVideoPlayPause(video);
        });

        // 添加悬停效果
        playOverlay.addEventListener('mouseenter', () => {
            playOverlay.style.transform = 'translate(-50%, -50%) scale(1.1)';
            playOverlay.style.background = 'rgba(0, 0, 0, 0.8)';
        });

        playOverlay.addEventListener('mouseleave', () => {
            playOverlay.style.transform = 'translate(-50%, -50%) scale(1)';
            playOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
        });

        video.parentElement.appendChild(playOverlay);
    }

    showVolumeHint() {
        // 显示音量提示
        const hint = document.createElement('div');
        hint.className = 'volume-hint';
        hint.innerHTML = '<i class="fas fa-volume-mute"></i> 点击取消静音';
        hint.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 15;
            backdrop-filter: blur(10px);
            animation: fadeInOut 3s ease;
        `;

        document.body.appendChild(hint);

        // 3秒后自动移除
        setTimeout(() => {
            if (hint.parentElement) {
                hint.remove();
            }
        }, 3000);
    }

    nextContent() {
        if (this.isTransitioning) return;

        // 立即暂停所有媒体
        this.pauseAllMedia();

        // 检查是否需要加载更多内容
        if (this.currentIndex >= this.mediaData.length - 3 && this.hasMore && !this.isLoading) {
            this.loadMediaData(true); // 提前加载更多内容
        }

        // 检查是否已经到达最后一个内容
        if (this.currentIndex >= this.mediaData.length - 1) {
            if (this.hasMore) {
                // 如果还有更多内容但正在加载，显示加载提示
                this.showLoadingMore();
                return;
            } else {
                // 没有更多内容，显示结束提示
                this.showEndMessage();
                return;
            }
        }

        this.isTransitioning = true;
        this.currentIndex++;
        this.currentImageIndex = 0;
        this.updateActiveContent();
        this.updateBottomInfo();

        setTimeout(() => {
            this.isTransitioning = false;
        }, 300);
    }

    previousContent() {
        if (this.isTransitioning || this.currentIndex <= 0) return;

        // 立即暂停所有媒体
        this.pauseAllMedia();

        this.isTransitioning = true;
        this.currentIndex--;
        this.currentImageIndex = 0;
        this.updateActiveContent();
        this.updateBottomInfo();

        setTimeout(() => {
            this.isTransitioning = false;
        }, 300);
    }

    nextImage() {
        const currentMedia = this.getCurrentMedia();
        if (currentMedia.type !== 'images') return;

        const maxIndex = currentMedia.images.length - 1;
        if (this.currentImageIndex < maxIndex) {
            this.currentImageIndex++;
            this.updateImageSlider();
            this.updateImageIndicators();
        }
    }

    previousImage() {
        const currentMedia = this.getCurrentMedia();
        if (currentMedia.type !== 'images') return;

        if (this.currentImageIndex > 0) {
            this.currentImageIndex--;
            this.updateImageSlider();
            this.updateImageIndicators();
        }
    }

    updateImageSlider() {
        const currentItem = document.querySelector('.content-item.active');
        const slider = currentItem.querySelector('.image-slider');
        if (slider) {
            const translateX = -this.currentImageIndex * 100;
            slider.style.transform = `translateX(${translateX}%)`;
        }
    }

    updateImageNavigation() {
        const currentMedia = this.getCurrentMedia();
        const imageIndicators = document.getElementById('imageIndicators');

        if (currentMedia.type === 'images' && currentMedia.images.length > 1) {
            imageIndicators.style.display = 'flex';
            this.updateImageIndicators();
        } else {
            imageIndicators.style.display = 'none';
        }
    }

    updateImageIndicators() {
        const currentMedia = this.getCurrentMedia();
        if (currentMedia.type !== 'images') return;

        const indicatorsContainer = document.getElementById('imageIndicators');
        indicatorsContainer.innerHTML = '';

        currentMedia.images.forEach((_, index) => {
            const dot = document.createElement('div');
            dot.className = `image-dot ${index === this.currentImageIndex ? 'active' : ''}`;
            indicatorsContainer.appendChild(dot);
        });
    }

    updateBottomInfo() {
        const currentMedia = this.getCurrentMedia();

        document.getElementById('userAvatar').src = currentMedia.user.avatar;
        document.getElementById('username').textContent = `@${currentMedia.user.name}`;

        // 更新描述并检查是否需要展开按钮
        const descriptionElement = document.getElementById('description');
        const descriptionText = document.querySelector('.description-text');
        const expandBtn = document.getElementById('expandBtn');

        descriptionText.textContent = currentMedia.description;
        descriptionElement.classList.remove('expanded');

        // 检查文本是否超过两行
        setTimeout(() => {
            const lineHeight = parseInt(window.getComputedStyle(descriptionText).lineHeight);
            const maxHeight = lineHeight * 2;

            if (descriptionText.scrollHeight > maxHeight) {
                expandBtn.style.display = 'inline';
                expandBtn.textContent = '展开';
            } else {
                expandBtn.style.display = 'none';
            }
        }, 10);

        // 更新评论数量
        const contentId = currentMedia.id;
        const commentCount = this.getCommentCount(contentId);
        document.getElementById('commentCount').textContent = commentCount;
    }



    getCurrentMedia() {
        return this.mediaData[this.currentIndex];
    }

    formatCount(count) {
        if (count >= 1000000) {
            return (count / 1000000).toFixed(1) + 'M';
        } else if (count >= 1000) {
            return (count / 1000).toFixed(1) + 'K';
        }
        return count.toString();
    }

    togglePlayPause() {
        const currentItem = document.querySelector('.content-item.active');
        const video = currentItem.querySelector('video');

        if (video) {
            this.toggleVideoPlayPause(video);
        }
    }

    toggleVideoPlayPause(video) {
        if (video.paused) {
            // 确保不静音
            video.muted = false;
            video.play().then(() => {
                console.log('视频播放');
                // 移除播放按钮
                const playButton = video.parentElement.querySelector('.play-overlay');
                if (playButton) {
                    playButton.remove();
                }
            }).catch(e => {
                console.log('播放失败:', e);
                // 如果播放失败，可能是因为浏览器政策，尝试静音播放
                video.muted = true;
                video.play().then(() => {
                    console.log('静音播放成功');
                    // 显示音量提示
                    this.showVolumeHint();
                }).catch(err => {
                    console.log('静音播放也失败:', err);
                });
            });
        } else {
            video.pause();
            console.log('视频暂停');
            // 显示播放按钮
            this.showPlayButton(video);
        }
    }



    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showLoadingMore() {
        // 显示"正在加载更多..."的提示
        console.log('正在加载更多内容...');
        // 可以在这里添加UI提示
    }

    showError(message) {
        console.error(message);

        const errorToast = document.getElementById('errorToast');
        const errorMessage = document.getElementById('errorMessage');

        errorMessage.textContent = message;
        errorToast.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            errorToast.style.display = 'none';
        }, 3000);
    }

    handleOrientationChange() {
        // 强制重新计算布局
        const appContainer = document.querySelector('.app-container');
        appContainer.style.height = window.innerHeight + 'px';
        appContainer.style.width = window.innerWidth + 'px';

        // 重新播放当前媒体以适应新的尺寸
        setTimeout(() => {
            this.playCurrentMedia();
        }, 200);

        console.log('屏幕方向已改变:', window.innerWidth, 'x', window.innerHeight);
    }

    showEndMessage() {
        console.log('已到达内容末尾');
        document.getElementById('endMessage').style.display = 'flex';

        // 暂停所有媒体
        this.pauseAllMedia();
    }

    hideEndMessage() {
        document.getElementById('endMessage').style.display = 'none';
    }

    restartContent() {
        console.log('重新开始浏览内容');
        this.hideEndMessage();
        this.currentIndex = 0;
        this.currentImageIndex = 0;
        this.updateActiveContent();
        this.updateBottomInfo();

        // 播放第一个内容
        setTimeout(() => {
            this.playCurrentMedia();
        }, 100);
    }

    // 评论相关方法
    openCommentPanel() {
        const panel = document.getElementById('commentPanel');
        panel.style.display = 'flex';
        setTimeout(() => {
            panel.classList.add('show');
        }, 10);

        this.loadComments();

        // 暂停所有媒体
        this.pauseAllMedia();
    }

    closeCommentPanel() {
        const panel = document.getElementById('commentPanel');
        panel.classList.remove('show');
        setTimeout(() => {
            panel.style.display = 'none';
        }, 300);

        this.cancelReply();

        // 恢复视频播放
        const currentVideo = document.querySelector('.content-item.active video');
        if (currentVideo) {
            currentVideo.play();
        }
    }

    async loadComments() {
        const currentMedia = this.getCurrentMedia();
        const contentId = currentMedia.id;
        const commentList = document.getElementById('commentList');

        try {
            // 显示加载状态
            commentList.innerHTML = '<div class="loading-comments">正在加载评论...</div>';

            const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/comments?contentId=${contentId}&current=1&size=20`;
            const response = await this.fetchWithRetry(url);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.code !== 200) {
                throw new Error(`API error: ${result.message}`);
            }

            const comments = this.transformCommentsData(result.data.records || []);

            // 缓存评论数据
            this.comments[contentId] = comments;

            if (comments.length === 0) {
                commentList.innerHTML = `
                    <div class="empty-comments">
                        <i class="fas fa-comment"></i>
                        <div>还没有评论，快来抢沙发吧！</div>
                    </div>
                `;
                return;
            }

            commentList.innerHTML = '';
            comments.forEach(comment => {
                const commentElement = this.createCommentElement(comment);
                commentList.appendChild(commentElement);
            });

        } catch (error) {
            console.error('Failed to load comments:', error);
            commentList.innerHTML = `
                <div class="error-comments">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>评论加载失败，请重试</div>
                </div>
            `;
        }
    }

    createCommentElement(comment) {
        const commentDiv = document.createElement('div');
        commentDiv.className = 'comment-item';

        const repliesHtml = comment.replies ? comment.replies.map(reply => `
            <div class="reply-item">
                <div class="reply-avatar">
                    <img src="${reply.user.avatar}" alt="${reply.user.name}">
                </div>
                <div class="reply-content">
                    <div class="reply-user">@${reply.user.name}</div>
                    <div class="reply-text">${reply.text}</div>
                    <div class="reply-time">${this.formatTime(reply.timestamp)}</div>
                </div>
            </div>
        `).join('') : '';

        commentDiv.innerHTML = `
            <div class="comment-main">
                <div class="comment-avatar">
                    <img src="${comment.user.avatar}" alt="${comment.user.name}">
                </div>
                <div class="comment-content">
                    <div class="comment-user">@${comment.user.name}</div>
                    <div class="comment-text">${comment.text}</div>
                    <div class="comment-actions">
                        <span class="comment-time">${this.formatTime(comment.timestamp)}</span>
                        <button class="comment-reply-btn" onclick="app.replyToComment('${comment.id}', '${comment.user.name}')">回复</button>
                    </div>
                </div>
            </div>
            ${repliesHtml ? `<div class="comment-replies">${repliesHtml}</div>` : ''}
        `;

        return commentDiv;
    }

    // 转换评论数据格式
    transformCommentsData(apiComments) {
        return apiComments.map(comment => ({
            id: comment.id.toString(),
            user: {
                name: comment.username || 'Anonymous',
                avatar: comment.avatarUrl || 'https://picsum.photos/50/50?random=' + comment.userId
            },
            text: comment.commentText,
            timestamp: comment.createdAt,
            replies: comment.replies ? comment.replies.map(reply => ({
                id: reply.id.toString(),
                user: {
                    name: reply.username || 'Anonymous',
                    avatar: reply.avatarUrl || 'https://picsum.photos/50/50?random=' + reply.userId
                },
                text: reply.commentText,
                timestamp: reply.createdAt
            })) : []
        }));
    }

    async sendComment() {
        const input = document.getElementById('commentInput');
        const text = input.value.trim();

        if (!text) return;

        const currentMedia = this.getCurrentMedia();
        const contentId = currentMedia.id;

        // 禁用发送按钮，防止重复提交
        const sendBtn = document.getElementById('commentSend');
        sendBtn.disabled = true;
        sendBtn.textContent = '发送中...';

        try {
            const requestData = {
                contentId: contentId,
                userId: this.currentUserId,
                commentText: text
            };

            // 如果是回复评论，添加parentId
            if (this.replyingTo) {
                requestData.parentId = parseInt(this.replyingTo.id);
            }

            const url = `${API_CONFIG.BASE_URL}${API_CONFIG.API_PATH}/comments`;
            const response = await this.fetchWithRetry(url, {
                method: 'POST',
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.code !== 200) {
                throw new Error(`API error: ${result.message}`);
            }

            console.log('评论发布成功:', result);

            // 清空输入框
            input.value = '';
            this.cancelReply();

            // 重新加载评论列表
            await this.loadComments();
            this.updateBottomInfo();

            // 滚动到底部
            const commentList = document.getElementById('commentList');
            setTimeout(() => {
                commentList.scrollTop = commentList.scrollHeight;
            }, 100);

        } catch (error) {
            console.error('Failed to send comment:', error);
            this.showError('评论发布失败，请重试');
        } finally {
            // 恢复发送按钮
            sendBtn.disabled = false;
            sendBtn.textContent = '发送';
        }
    }

    replyToComment(commentId, userName) {
        this.replyingTo = { id: commentId, userName: userName };

        const replyInfo = document.getElementById('replyInfo');
        const replyTarget = document.getElementById('replyTarget');

        replyTarget.textContent = `@${userName}`;
        replyInfo.style.display = 'flex';

        document.getElementById('commentInput').focus();
    }

    cancelReply() {
        this.replyingTo = null;
        document.getElementById('replyInfo').style.display = 'none';
    }

    getCommentCount(contentId) {
        if (!this.comments[contentId]) return 0;

        let count = this.comments[contentId].length;
        this.comments[contentId].forEach(comment => {
            if (comment.replies) {
                count += comment.replies.length;
            }
        });

        return count;
    }

    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;

        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return time.toLocaleDateString();
    }



    toggleDescription() {
        const descriptionElement = document.getElementById('description');
        const expandBtn = document.getElementById('expandBtn');

        if (descriptionElement.classList.contains('expanded')) {
            // 收起
            descriptionElement.classList.remove('expanded');
            expandBtn.textContent = '展开';
        } else {
            // 展开
            descriptionElement.classList.add('expanded');
            expandBtn.textContent = '收起';
        }
    }
}

// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TikTokClone();
});
