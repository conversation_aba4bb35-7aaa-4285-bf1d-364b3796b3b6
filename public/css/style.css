* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #000;
    color: #fff;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

.app-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.content-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.content-item {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-item.active {
    transform: translateY(0);
}

.content-item.prev {
    transform: translateY(-100%);
}

.content-item.next {
    transform: translateY(100%);
}

/* 视频样式 */
.video-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
    max-width: 100vw;
    max-height: 100vh;
    background: #000;
}

/* 视频封面样式 */
.video-container video[poster] {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* 图片集样式 */
.image-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: #000;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-slider {
    width: 100%;
    height: 100%;
    display: flex;
    transition: transform 0.3s ease;
}

.image-slide {
    min-width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.image-slide img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}



/* 右侧操作栏 */
.right-sidebar {
    position: fixed;
    right: 15px;
    bottom: 120px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment-btn {
    background: rgba(0, 0, 0, 0.6);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    color: white;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.comment-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.comment-count {
    font-size: 10px;
    font-weight: 600;
    margin-top: 2px;
}

/* 底部用户信息 */
.bottom-user-info {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 90px;
    z-index: 100;
    max-width: calc(100vw - 110px);
}

.username {
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    margin-bottom: 6px;
    color: #fff;
}

.description {
    font-size: 14px;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    color: rgba(255, 255, 255, 0.95);
    word-wrap: break-word;
}

.description-text {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.description.expanded .description-text {
    display: block;
    -webkit-line-clamp: unset;
}

.expand-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    cursor: pointer;
    padding: 0;
    margin-left: 4px;
    text-decoration: underline;
}

.expand-btn:hover {
    color: #fff;
}



/* 图片集指示器 */
.image-indicators {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    display: flex;
    gap: 8px;
    background: rgba(0, 0, 0, 0.3);
    padding: 8px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.image-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
}

.image-dot.active {
    background: #fff;
    transform: scale(1.3);
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 200;
    display: none;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 播放覆盖层 */
.play-overlay {
    transition: all 0.3s ease;
    animation: playButtonPulse 2s infinite;
}

.play-overlay:hover {
    transform: translate(-50%, -50%) scale(1.1);
    background: rgba(0, 0, 0, 0.8);
    animation: none;
}

@keyframes playButtonPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.9;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        opacity: 1;
    }
}

/* 音量提示动画 */
@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    20%, 80% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
}

/* 视频加载状态 */
video {
    transition: opacity 0.3s ease;
}

video:not([data-loaded="true"]) {
    opacity: 0.7;
}

/* 图片加载动画 */
.image-slide img {
    transition: opacity 0.3s ease;
    opacity: 0;
}

.image-slide img.loaded {
    opacity: 1;
}

/* 内容项过渡优化 */
.content-item {
    will-change: transform;
    backface-visibility: hidden;
}

/* 内容结束提示 */
.end-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 200;
    display: flex;
    align-items: center;
    justify-content: center;
}

.end-content {
    text-align: center;
    padding: 40px 20px;
}

.end-icon {
    font-size: 60px;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.end-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #fff;
}

.end-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 30px;
}

.restart-btn {
    background: linear-gradient(45deg, #ff3040, #ff6b7a);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 48, 64, 0.3);
}

.restart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 48, 64, 0.4);
}

.restart-btn:active {
    transform: translateY(0);
}

/* 评论面板 */
.comment-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70vh;
    background: #fff;
    border-radius: 20px 20px 0 0;
    z-index: 300;
    display: flex;
    flex-direction: column;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.comment-panel.show {
    transform: translateY(0);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 10px;
    border-bottom: 1px solid #f0f0f0;
}

.comment-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.comment-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.comment-close:hover {
    background: #f0f0f0;
}

.comment-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px 20px;
}

.comment-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.comment-main {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
}

.comment-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment-content {
    flex: 1;
}

.comment-user {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.comment-text {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

.comment-actions {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #999;
}

.comment-time {
    color: #999;
}

.comment-reply-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
}

.comment-reply-btn:hover {
    color: #666;
}

.comment-replies {
    margin-left: 44px;
    margin-top: 12px;
}

.reply-item {
    display: flex;
    gap: 10px;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.reply-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.reply-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reply-content {
    flex: 1;
}

.reply-user {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.reply-text {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.reply-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
}

.comment-input-area {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
}

.comment-input-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.comment-input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 10px 15px;
    font-size: 14px;
    outline: none;
    background: #f8f9fa;
}

.comment-input:focus {
    border-color: #ff3040;
    background: #fff;
}

.comment-send {
    background: #ff3040;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.comment-send:hover {
    background: #e02030;
    transform: scale(1.05);
}

.comment-send:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.reply-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    background: #f0f8ff;
    border-radius: 8px;
    margin-bottom: 10px;
    font-size: 12px;
}

.reply-text {
    color: #666;
}

.reply-cancel {
    background: none;
    border: none;
    color: #ff3040;
    cursor: pointer;
    font-size: 12px;
    padding: 0;
}

.empty-comments {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-comments i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ddd;
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: #ff4757;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
    animation: slideDown 0.3s ease;
}

.error-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.error-message {
    font-size: 14px;
    font-weight: 500;
}

@keyframes slideDown {
    from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

/* 评论加载和错误状态 */
.loading-comments,
.error-comments {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.error-comments {
    color: #ff4757;
}

.error-comments i {
    font-size: 32px;
    margin-bottom: 12px;
}

/* 滚动条隐藏 */
::-webkit-scrollbar {
    display: none;
}

* {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
    .right-sidebar {
        right: 10px;
        bottom: 100px;
        gap: 15px;
    }

    .avatar {
        width: 45px;
        height: 45px;
        border-width: 2px;
    }

    .comment-btn {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .bottom-user-info {
        bottom: 15px;
        left: 15px;
        right: 75px;
    }

    .username {
        font-size: 15px;
    }

    .description {
        font-size: 13px;
    }

    .end-title {
        font-size: 20px;
    }

    .end-subtitle {
        font-size: 14px;
    }

    .restart-btn {
        padding: 10px 25px;
        font-size: 14px;
    }

    .image-slide {
        padding: 15px;
    }
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .right-sidebar {
        right: 10px;
        bottom: 80px;
        gap: 12px;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-width: 2px;
    }

    .comment-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .bottom-user-info {
        bottom: 10px;
        left: 15px;
        right: 65px;
    }

    .username {
        font-size: 14px;
        margin-bottom: 4px;
    }

    .description {
        font-size: 12px;
    }

    .description-text {
        -webkit-line-clamp: 1;
    }

    .image-indicators {
        bottom: 60px;
        padding: 6px 10px;
    }

    .image-dot {
        width: 6px;
        height: 6px;
    }

    .image-slide {
        padding: 10px;
    }

    .end-icon {
        font-size: 50px;
        margin-bottom: 15px;
    }

    .end-title {
        font-size: 18px;
    }

    .end-subtitle {
        font-size: 13px;
        margin-bottom: 20px;
    }
}

/* 竖屏模式优化 */
@media screen and (orientation: portrait) {
    .content-item {
        width: 100%;
        height: 100%;
    }

    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .image-slide img {
        max-width: 100%;
        max-height: calc(100vh - 160px);
        object-fit: contain;
    }
}

/* 小屏幕设备 */
@media screen and (max-width: 375px) {
    .right-sidebar {
        right: 8px;
        bottom: 90px;
        gap: 12px;
    }

    .avatar {
        width: 42px;
        height: 42px;
    }

    .comment-btn {
        width: 42px;
        height: 42px;
        font-size: 15px;
    }

    .bottom-user-info {
        bottom: 12px;
        left: 12px;
        right: 65px;
    }

    .username {
        font-size: 14px;
    }

    .description {
        font-size: 12px;
    }

    .image-slide {
        padding: 12px;
    }

    .end-content {
        padding: 30px 15px;
    }

    .end-icon {
        font-size: 50px;
    }

    .end-title {
        font-size: 18px;
    }

    .restart-btn {
        padding: 8px 20px;
        font-size: 13px;
    }
}
