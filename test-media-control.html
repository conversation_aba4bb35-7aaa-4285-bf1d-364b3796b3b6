<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>媒体控制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .video-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .video-item {
            flex: 1;
            text-align: center;
        }
        video {
            width: 100%;
            max-width: 300px;
            height: 200px;
            background: #000;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
        }
        .playing {
            color: #28a745;
        }
        .paused {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>媒体控制功能测试</h1>
    
    <div class="test-section">
        <h3>测试视频</h3>
        <div class="video-container">
            <div class="video-item">
                <h4>视频 1</h4>
                <video id="video1" controls>
                    <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
                <div id="status1" class="status">状态: 未播放</div>
            </div>
            <div class="video-item">
                <h4>视频 2</h4>
                <video id="video2" controls>
                    <source src="https://www.w3schools.com/html/movie.mp4" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
                <div id="status2" class="status">状态: 未播放</div>
            </div>
            <div class="video-item">
                <h4>视频 3</h4>
                <video id="video3" controls>
                    <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>
                <div id="status3" class="status">状态: 未播放</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>控制按钮</h3>
        <button class="test-button" onclick="playVideo(1)">播放视频1</button>
        <button class="test-button" onclick="playVideo(2)">播放视频2</button>
        <button class="test-button" onclick="playVideo(3)">播放视频3</button>
        <br>
        <button class="test-button" onclick="pauseAllVideos()">暂停所有视频</button>
        <button class="test-button" onclick="simulateContentSwitch()">模拟内容切换</button>
        <button class="test-button" onclick="checkAllStatus()">检查所有状态</button>
    </div>

    <div class="test-section">
        <h3>测试说明</h3>
        <ul>
            <li><strong>播放视频</strong>: 点击播放按钮播放指定视频</li>
            <li><strong>暂停所有视频</strong>: 测试一键暂停所有正在播放的视频</li>
            <li><strong>模拟内容切换</strong>: 模拟短视频应用中的内容切换，应该暂停当前视频并播放下一个</li>
            <li><strong>检查所有状态</strong>: 显示所有视频的当前播放状态</li>
        </ul>
    </div>

    <script>
        let currentVideoIndex = 0;
        const videos = [
            document.getElementById('video1'),
            document.getElementById('video2'),
            document.getElementById('video3')
        ];

        // 监听视频状态变化
        videos.forEach((video, index) => {
            video.addEventListener('play', () => updateStatus(index + 1, '播放中', 'playing'));
            video.addEventListener('pause', () => updateStatus(index + 1, '已暂停', 'paused'));
            video.addEventListener('ended', () => updateStatus(index + 1, '播放结束', 'paused'));
            video.addEventListener('loadstart', () => updateStatus(index + 1, '加载中...', ''));
        });

        function updateStatus(videoNum, status, className) {
            const statusElement = document.getElementById(`status${videoNum}`);
            statusElement.textContent = `状态: ${status}`;
            statusElement.className = `status ${className}`;
        }

        function playVideo(videoNum) {
            const video = videos[videoNum - 1];
            video.currentTime = 0;
            video.play().then(() => {
                console.log(`视频${videoNum}开始播放`);
            }).catch(e => {
                console.error(`视频${videoNum}播放失败:`, e);
                updateStatus(videoNum, '播放失败', 'paused');
            });
        }

        function pauseAllVideos() {
            console.log('暂停所有视频...');
            videos.forEach((video, index) => {
                if (!video.paused) {
                    video.pause();
                    video.currentTime = 0;
                    console.log(`暂停视频${index + 1}`);
                }
            });
        }

        function simulateContentSwitch() {
            console.log('模拟内容切换...');
            
            // 暂停所有视频
            pauseAllVideos();
            
            // 切换到下一个视频
            currentVideoIndex = (currentVideoIndex + 1) % videos.length;
            
            setTimeout(() => {
                playVideo(currentVideoIndex + 1);
                console.log(`切换到视频${currentVideoIndex + 1}`);
            }, 500);
        }

        function checkAllStatus() {
            console.log('检查所有视频状态:');
            videos.forEach((video, index) => {
                const status = video.paused ? '暂停' : '播放中';
                const currentTime = video.currentTime.toFixed(2);
                console.log(`视频${index + 1}: ${status}, 当前时间: ${currentTime}s`);
            });
        }

        // 页面失去焦点时暂停所有视频
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                pauseAllVideos();
                console.log('页面失去焦点，暂停所有视频');
            }
        });

        window.addEventListener('blur', () => {
            pauseAllVideos();
            console.log('窗口失去焦点，暂停所有视频');
        });
    </script>
</body>
</html>
