{"name": "tiktok-clone", "version": "1.0.0", "description": "A TikTok-like web application with video and image carousel support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["tiktok", "video", "social", "web", "nodejs"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.0.1"}}