# TikTok Clone - 简洁版移动端应用

一个专为移动端优化的仿 TikTok 网页应用，支持竖屏横屏切换，上下滑动切换视频和图片集浏览。已对接真实API服务，支持动态内容加载和评论互动。

## ✨ 功能特性

- 📱 **移动端优先** - 专门为手机端设计，完美适配各种屏幕尺寸
- 🔄 **竖屏横屏支持** - 自动适配屏幕方向变化
- 🎥 **智能视频播放** - 首次不自动播放，切换时自动播放，点击控制
- 🖼️ **图片集浏览** - 支持多图片轮播，纯手势左右滑动切换
- 👆 **优化触摸手势** - 流畅的上下滑动切换内容，左右滑动切换图片
- 💬 **两层评论系统** - 支持评论和回复，完整的互动体验
- 📝 **智能描述展开** - 描述默认两行，超出可点击展开显示全部
- 🔄 **无限刷新体验** - 内容刷完显示提示，支持重新开始
- 🎨 **TikTok式布局** - 右侧操作栏，底部用户信息，经典设计
- 📺 **内容完整显示** - 优化媒体显示，确保内容完整可见
- ⚡ **性能优化** - 针对移动端优化的加载和渲染性能
- 🔌 **API对接** - 对接真实短视频服务API，支持动态内容和评论

## 🚀 快速开始

### 环境要求

- Node.js 20.x 或更高版本
- npm 或 yarn

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ShortVideo
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm start
   ```
   或者使用 nodemon 进行开发：
   ```bash
   npm run dev
   ```

4. **访问应用**
   - 桌面端：打开浏览器访问 `http://localhost:3000`
   - 移动端：在手机浏览器中访问 `http://你的电脑IP:3000`
   - 测试页面：访问 `mobile-test.html` 查看设备信息

## 🔌 API对接说明

### API服务器配置
- **服务地址**: `http://*************:8080`
- **API基础路径**: `/api/v1`
- **数据格式**: JSON
- **请求头**: `Content-Type: application/json`

### 已对接的接口
1. **内容列表**: `GET /api/v1/contents` - 获取短视频内容列表
2. **评论列表**: `GET /api/v1/contents/{id}/comments` - 获取评论
3. **发布评论**: `POST /api/v1/comments` - 发布评论和回复

### 特性
- **自动重试**: 网络错误时自动重试3次
- **分页加载**: 支持滑动加载更多内容
- **错误处理**: 用户友好的错误提示
- **数据转换**: 自动转换API数据格式
- **降级处理**: API不可用时使用示例数据

## 🎮 操作指南

### 移动端操作（主要）
- **首次进入** - 显示播放按钮，点击开始播放
- **上下滑动** - 切换视频/图片集内容，自动播放视频
- **左右滑动** - 在图片集中切换图片（纯手势，无按钮）
- **点击视频** - 播放/暂停控制
- **点击头像** - 查看用户信息（右侧头像）
- **点击评论按钮** - 打开评论面板（右侧圆形按钮）
- **点击描述** - 展开/收起长文本描述（底部描述区域）
- **发表评论** - 在评论面板中输入和发送评论
- **回复评论** - 点击回复按钮回复特定评论
- **滑到最后** - 显示结束提示，可重新开始
- **旋转屏幕** - 自动适配竖屏/横屏模式

### 桌面端操作（辅助）
- **上下方向键** - 切换上一个/下一个内容
- **左右方向键** - 在图片集中切换图片
- **空格键** - 播放/暂停视频

## 📁 项目结构

```
ShortVideo/
├── public/                 # 静态文件目录
│   ├── css/
│   │   └── style.css      # 主样式文件
│   ├── js/
│   │   └── app.js         # 主应用逻辑
│   ├── media/
│   │   ├── sample-data.json # 示例数据
│   │   ├── videos/        # 视频文件目录
│   │   └── images/        # 图片文件目录
│   └── index.html         # 主页面
├── server.js              # Express 服务器
├── package.json           # 项目配置
└── README.md             # 项目说明
```

## 🔧 API 接口

### 获取媒体数据
```
GET /api/media
```

### 获取单个媒体项
```
GET /api/media/:id
```

### 点赞
```
POST /api/media/:id/like
```

### 获取评论
```
GET /api/media/:id/comments
```

### 添加评论
```
POST /api/media/:id/comments
```

### 分享统计
```
POST /api/media/:id/share
```

### 搜索内容
```
GET /api/search?q=关键词
```

### 获取统计信息
```
GET /api/stats
```

## 🎨 自定义内容

### 添加自己的媒体内容

1. **添加视频**
   - 将视频文件放入 `public/media/videos/` 目录
   - 在 `public/media/sample-data.json` 中添加视频信息

2. **添加图片**
   - 将图片文件放入 `public/media/images/` 目录
   - 在 `public/media/sample-data.json` 中添加图片集信息

### 数据格式示例

```json
{
  "type": "video",
  "src": "media/videos/your-video.mp4",
  "poster": "media/images/your-poster.jpg",
  "user": {
    "name": "your_username",
    "avatar": "media/images/your-avatar.jpg"
  },
  "description": "视频描述 #标签",
  "music": "背景音乐名称",
  "stats": {
    "likes": 0,
    "comments": 0,
    "shares": 0
  }
}
```

## 🛠️ 技术栈

- **后端**: Node.js + Express
- **前端**: 原生 HTML5 + CSS3 + JavaScript
- **样式**: CSS3 动画和过渡效果
- **字体**: Google Fonts (Inter)
- **图标**: Font Awesome

## 📱 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
